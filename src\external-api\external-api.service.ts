import { Injectable } from '@nestjs/common';
import { ReferenceDataBryzosTermsConditions, User, UserWebhookSubscription } from '@bryzos/extended-widget-library';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DataBaseService } from '@bryzos/base-library';
import { Constants } from 'src/Constants';
import { CognitoAuthService } from 'src/CognitoAuthService';
import { AwsTextractService } from 'src/AwsTextractService';


@Injectable()
export class ExternalApiService {
  private dbObj = new DataBaseService();
  private auth = new CognitoAuthService();

  constructor(@InjectRepository(ReferenceDataBryzosTermsConditions) private readonly refBryzosTermsCondition: Repository<ReferenceDataBryzosTermsConditions>,
  @InjectRepository(UserWebhookSubscription) private readonly userWebhookSubscription: Repository<UserWebhookSubscription>,
  @InjectRepository(User) private readonly userRepository: Repository<User>,
  private readonly textract: AwsTextractService,

  ) 
  {}

  async findAll() {
    let bryzosTermsConditions = await this.dbObj.findManyWithOrComparision(this.refBryzosTermsCondition,"is_active","type",[Constants.BUYER,Constants.SELLER]);
    let response = {
      "ref_bryzos_terms_conditions": bryzosTermsConditions,
    }
    return response;
  }

  async getAccessToken(payload) {
    let response = await this.auth.authenticateUser(payload.email, payload.password);
    return response;
  }

  async subscribeWebhook(userId, payload)
  {
    let response = null;
    let user = await this.dbObj.findOne(this.userRepository, 'id', userId);
    if(!user) {
      return { "error_message": "User not found" };
    }

    let companyId = user.company_id;
    //deactivate if any other webhook url's exists
    await this.dbObj.updateByMultipleWhere({'is_active': false},{'company_id': companyId, 'event': payload.event}, this.userWebhookSubscription);

    //TODO: get webhook events from DB
    let webhookId = await this.dbObj.saveWithOutMapper({'user_id': userId,'company_id': companyId, 'webhook_url': payload.webhook_url, 'event': payload.event}, userId, this.userWebhookSubscription);

    if(webhookId)
      response = "Successfully subscribed to webhook. Your webhook id is "+ webhookId;

    return response;
  }

  async unsubscribeWebhook(userId, webhookId)
  {
    let response = null;
    let user = await this.dbObj.findOne(this.userRepository, 'id', userId);
    if(!user) {
      return { "error_message": "User not found" };
    }

    let companyId = user.company_id;
    //unsubscribe webhook url's
    let updateResponse = await this.dbObj.updateByMultipleWhere({'is_active': false,'is_subscribed': false},{'company_id': companyId, 'id': webhookId, 'user_id': userId}, this.userWebhookSubscription);

    if(updateResponse) 
      response = "Successfully unsubscribed to webhook.";
    else 
      response = "This webhook does not belong to you";
    

    return response;
  }

  async textExtractFromFile(s3key: string){
    try{
      let fileName = s3key;
      const jobId = await this.textract.startAnalysis(fileName);
      console.log('Started Textract job:', jobId);

      // Get analysis results
      const results = await this.textract.getAnalysisResults(jobId);
      console.log(`Retrieved ${results.Blocks.length} blocks from Textract`);
      return results;
    }catch(error){
      return { error_message : "Something went wrong!!" };
    }
  }
}
