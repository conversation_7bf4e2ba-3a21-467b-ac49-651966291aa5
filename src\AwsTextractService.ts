import { AwsUtilityV3, <PERSON>LibraryService, <PERSON><PERSON>zosLogger } from "@bryzos/base-library";
import { Injectable } from "@nestjs/common";
import  { TextractClient, AnalyzeDocumentCommand, FeatureType, StartDocumentTextDetectionCommand, StartDocumentAnalysisCommand, GetDocumentAnalysisCommand, StartExpenseAnalysisCommand, GetDocumentTextDetectionCommand, GetExpenseAnalysisCommand } from "@aws-sdk/client-textract";
import { log } from "console";

@Injectable()
export class AwsTextractService {

    private awsAccessKeys: { AWS_CREDENTIAL_KEY:string, AWS_CREDENTIAL_SECRET:string }
    private textractClient:any;
    private readonly region = process.env.AWS_REGION;
    private smEnv: string;
    private smCodedVendor: string;
    private smCodedKey: string;

    constructor(
        private readonly baseService:BaseLibraryService,
        private readonly awsUtility:AwsUtilityV3
      ) {
        this.smEnv = process.env.SM_ENV;
        this.smCodedVendor = 'AWS';
        this.smCodedKey = 'CREDENTIAL';
        this.getAwsKeys();
      }
    
    async getAwsKeys(){
        try {
            let keys = await this.baseService.getSecretValue(this.smEnv,this.smCodedVendor,this.smCodedKey);
            if(!keys){ return false; }
            this.awsAccessKeys = keys;
            console.log(this.awsAccessKeys);
        } catch(error){
            console.log("CognitoAutService error", error);
        }
    }

  async setTextractClientClient() {
    const awsKeys = this.awsAccessKeys; 
    return this.textractClient = new TextractClient({
      region: this.region,
      credentials: {
        accessKeyId: awsKeys.AWS_CREDENTIAL_KEY,
        secretAccessKey: awsKeys.AWS_CREDENTIAL_SECRET,
      },
    });
  }
    
  async startAnalysis(documentName: string) {
    await this.setTextractClientClient();
    const bucket = process.env.EXTRACT_INVOICE_S3_BUCKET;
    const roleArn = "arn:aws:iam::245262708136:role/TextractRole";
    const snsTopicArn = 'arn:aws:sns:us-east-1:245262708136:InvoiceTextExtraction';
    
    try {
      const { JobId } = await this.textractClient.send(new StartDocumentAnalysisCommand({DocumentLocation:{S3Object:{Bucket:bucket, Name:documentName}},FeatureTypes: [FeatureType.TABLES, FeatureType.LAYOUT], NotificationChannel:{RoleArn: roleArn, SNSTopicArn: snsTopicArn}}))
      return JobId;
    } catch (error) {
      console.log("Invoice extract error", error);
    }

  }
    
  async catchErrorLogs(error:any) {
    const currentDateTime = new Date();
    const UtcTime = currentDateTime.toISOString();
    const logglyData = JSON.stringify({'date': UtcTime,'errorMessage': error?.message,'data': error?.stack});
    console.log(process.env.NODE_ENV+'_'+'AWS_TEXTRACT_SERVICE_ERROR');
    BryzosLogger.log(logglyData, process.env.NODE_ENV+'_'+'AWS_TEXTRACT_SERVICE_ERROR');
  }

  // Poll until job completes and return all results
  async getAnalysisResults(jobId) {
    await this.setTextractClientClient();

    // Wait for job to complete
    let res;
    do {
      await new Promise((r) => setTimeout(r, 5000));
      res = await this.textractClient.send(
        new GetDocumentAnalysisCommand({ JobId: jobId })
      );
      console.log('Textract job status:', res.JobStatus);
    } while (res.JobStatus === 'IN_PROGRESS');

    if (res.JobStatus !== 'SUCCEEDED')
      throw new Error(`Textract job failed: ${res.JobStatus}`);

    // Collect all blocks from all response pages
    let allBlocks = [...res.Blocks];
    let nextToken = res.NextToken;

    // Continue fetching if there are more pages of results
    while (nextToken) {
      console.log('Fetching next page of Textract results...');
      res = await this.textractClient.send(
        new GetDocumentAnalysisCommand({
          JobId: jobId,
          NextToken: nextToken,
        })
      );
      allBlocks = [...allBlocks, ...res.Blocks];
      nextToken = res.NextToken;
      console.log(
        `Retrieved ${res.Blocks.length} more blocks, total now: ${allBlocks.length}`
      );
    }

    // Create a complete response object
    const completeResponse = {
      JobId: jobId,
      Status: 'SUCCEEDED',
      DocumentMetadata: res.DocumentMetadata,
      Blocks: allBlocks,
    };

    return completeResponse;
  }

}