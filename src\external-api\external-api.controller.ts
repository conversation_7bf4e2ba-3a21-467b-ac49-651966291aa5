import { Controller, Get, Post, Body, Patch, Param, Delete, Response, UsePipes, ValidationPipe, Query } from '@nestjs/common';
import { ExternalApiService } from './external-api.service';
import { Constants } from 'src/Constants';
import { SaveWebhookSubscribeDto } from 'src/external-api/dto/webhook-subscribe.dto';

const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;
@Controller('external-api')
export class ExternalApiController {
  constructor(private readonly externalApiService: ExternalApiService) {}

  @Get('v1/getTermsAndConditions')
  async findAll(@Response() res) {
    let responseData = {
      [responseTag]: await this.externalApiService.findAll()
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('v1/getAccessToken')
  async getAccessToken(@Body() payload, @Response() res) {
    let payloadData = payload[payloadTag];
    let responseData = {
      [responseTag]: await this.externalApiService.getAccessToken(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('subscribe/webhook')
  @UsePipes(ValidationPipe)
  async subscribeWebhook(@Body() saveWebhookSubscribeDto:SaveWebhookSubscribeDto ,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let payloadData = saveWebhookSubscribeDto[payloadTag];
    let responseData = {
      [responseTag]: await this.externalApiService.subscribeWebhook(userId, payloadData)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('unsubscribe/webhook/:webhookId')
  @UsePipes(ValidationPipe)
  async unsubscribeWebhook(@Param('webhookId') webhookId: string,@Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.externalApiService.unsubscribeWebhook(userId, webhookId)
    }
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Get('textExtractFromFile')
  async textExtractFromFile(@Response() res, @Query('s3key') s3key: string) {
    let responseData = {
      [responseTag]: await this.externalApiService.textExtractFromFile(s3key)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

}
